$(function () {
  getMyIp();
  getMyLocation();
  initProbeDetection();

  // query ip address function
  function getIp() {
    const ip = $("#ip-address").val();
    $.ajax({
      type: "GET",
      url: "/ip/ip",
      data: {
        ip: ip,
      },
      success: function (res, status) {
        $("#show-info").html("<h4>" + res + "</h4>");
      },
      dataType: "text",
    });
  }

  // input lose focus
  $("#ip-address")
    .blur(function () {
      const $parent = $(this).parent();
      $parent.find(".msg").remove();

      // check ip
      if ($(this).is("#ip-address")) {
        const psdVal = $.trim(this.value);
        const regIpv4 =
          /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/;
        const regIpv6 = /^([\da-fA-F]{1,4}:){7}[\da-fA-F]{1,4}$|^::1$|^::$/;

        if (psdVal === "" || (!regIpv4.test(psdVal) && !regIpv6.test(psdVal))) {
          const errorMsg = "请输入合法的ip地址！";
          $parent.append("<span class='msg red0'>" + errorMsg + "</span>");
          $("#btnSubmit").attr("disabled", true);
        } else {
          $("#btnSubmit").attr("disabled", false);
        }
      }
    })
    .keyup(function () {
      $(this).triggerHandler("blur");
    })
    .focus(function () {
      $(this).triggerHandler("blur");
    });

  $("#btnSubmit").click(function () {
    getIp();
    $("#ip-address").trigger("blur");
  });
});

// 获取myip的方法
function getMyIp() {
  $.ajax({
    type: "GET",
    url: "/ip/myip",
    success: function (res, status) {
      $("#show-myip").html(
        "您的 IP 地址是: " +
          '<a href="/ip/myip" class="boldfont">' +
          res +
          "</a>"
      );
    },
    dataType: "text",
  });
}

// 获取mylocation的方法
function getMyLocation() {
  $.ajax({
    type: "GET",
    url: "/ip/mylocation",
    success: function (res, status) {
      let html = "";
      let locationText = res;

      if (res.includes("校园网")) {
        const index = res.lastIndexOf("校园网");
        locationText = res.substring(0, index).trim();

        html = `
                    <div class="location-box location-campus fade-in">
                        <span>${locationText}</span>
                        <span class="badge-campus">校园网</span>
                    </div>`;
      } else {
        html = `
                    <div class="location-box fade-in">
                        <span>${res}</span>
                    </div>`;
      }
      $("#show-mylocation").html(html);
    },
    dataType: "text",
  });
}

// 多节点探测功能
function initProbeDetection() {
  // 清除可能存在的禁用样式
  $("#ip-global")
    .removeClass("disabled")
    .closest(".probe-card")
    .removeClass("probe-card-disabled");
  $("#ip-google")
    .removeClass("disabled")
    .closest(".probe-card")
    .removeClass("probe-card-disabled");

  // 国内IP检测 - 始终显示，使用多API备用机制
  getProbeIpWithFallback(
    [
      {
        url: "https://myip.ipip.net/",
        extractor: function (text) {
          const match = text.match(
            /(\d+\.\d+\.\d+\.\d+).*?(中国|台湾|香港|澳门)/
          );
          return {
            ip: match ? match[1] : null,
            location: match ? match[2] : null,
          };
        },
      },
      {
        url: "https://api.ip.sb/ip",
        extractor: function (text) {
          const ip = text.trim();
          if (/^\d+\.\d+\.\d+\.\d+$/.test(ip)) {
            return {
              ip: ip,
              location: "中国",
            };
          }
          return null;
        },
      },
      {
        url: "https://ipinfo.io/ip",
        extractor: function (text) {
          const ip = text.trim();
          if (/^\d+\.\d+\.\d+\.\d+$/.test(ip)) {
            return {
              ip: ip,
              location: "中国",
            };
          }
          return null;
        },
      },
    ],
    "ip-cn",
    "status-cn"
  );

  // 国际IP检测 - 使用多API备用机制
  getProbeIpWithFallback(
    [
      {
        url: "https://api.ipify.org?format=json",
        extractor: function (text) {
          try {
            const data = JSON.parse(text);
            const ip = data.ip;
            if (ip && /^\d+\.\d+\.\d+\.\d+$/.test(ip)) {
              return {
                ip: ip,
                location: "国外",
              };
            }
            return null;
          } catch {
            return null;
          }
        },
      },
      {
        url: "https://api.ipify.org",
        extractor: function (text) {
          const ip = text.trim();
          if (/^\d+\.\d+\.\d+\.\d+$/.test(ip)) {
            return {
              ip: ip,
              location: "国外",
            };
          }
          return null;
        },
      },
      {
        url: "https://icanhazip.com",
        extractor: function (text) {
          const ip = text.trim();
          if (/^\d+\.\d+\.\d+\.\d+$/.test(ip)) {
            return {
              ip: ip,
              location: "国外",
            };
          }
          return null;
        },
      },
      {
        url: "https://httpbin.org/ip",
        extractor: function (text) {
          try {
            const data = JSON.parse(text);
            const ip = data.origin;
            if (ip && /^\d+\.\d+\.\d+\.\d+$/.test(ip)) {
              return {
                ip: ip,
                location: "国外",
              };
            }
            return null;
          } catch {
            return null;
          }
        },
      },
      {
        url: "https://api.my-ip.io/ip",
        extractor: function (text) {
          const ip = text.trim();
          if (/^\d+\.\d+\.\d+\.\d+$/.test(ip)) {
            return {
              ip: ip,
              location: "国外",
            };
          }
          return null;
        },
      },
      {
        url: "https://checkip.amazonaws.com",
        extractor: function (text) {
          const ip = text.trim();
          if (/^\d+\.\d+\.\d+\.\d+$/.test(ip)) {
            return {
              ip: ip,
              location: "AWS",
            };
          }
          return null;
        },
      },
      {
        url: "https://icy-wave-16d7.bl275197319.workers.dev/",
        extractor: function (text) {
          try {
            const data = JSON.parse(text);
            return {
              ip: data.ip || null,
              location: data.country || "国外",
            };
          } catch {
            return null;
          }
        },
      },
    ],
    "ip-global",
    "status-global"
  );

  // Google DNS解析 - 使用多API备用机制
  getProbeIpWithFallback(
    [
      {
        url: "https://dns.google/resolve?name=www.google.com&type=A",
        extractor: function (text) {
          try {
            const data = JSON.parse(text);
            if (data.Status === 0 && data.Answer && data.Answer.length > 0) {
              return {
                ip: data.Answer[0].data,
                location: "Google DNS",
              };
            }
            return null;
          } catch {
            return null;
          }
        },
      },
      {
        url: "https://cloudflare-dns.com/dns-query?name=www.google.com&type=A",
        extractor: function (text) {
          try {
            const data = JSON.parse(text);
            if (data.Status === 0 && data.Answer && data.Answer.length > 0) {
              return {
                ip: data.Answer[0].data,
                location: "Cloudflare DNS",
              };
            }
            return null;
          } catch {
            return null;
          }
        },
      },
    ],
    "ip-google",
    "status-google"
  );
}

// 备用API的探测函数
function getProbeIpWithFallback(apiList, elementId, statusId) {
  const ipElement = $("#" + elementId);
  const statusElement = $("#" + statusId);

  // 显示加载状态
  ipElement.text("").addClass("loading").removeClass("success");
  statusElement.removeClass("probe-status-error probe-status-success");

  let currentApiIndex = 0;

  function tryNextApi() {
    if (currentApiIndex >= apiList.length) {
      // 所有API都失败了
      ipElement.text("-");
      ipElement.removeClass("success loading");
      statusElement.removeClass("probe-status-loading");
      return;
    }

    const currentApi = apiList[currentApiIndex];

    $.ajax({
      type: "GET",
      url: currentApi.url,
      timeout: 5000, // 超时时间5秒
      success: function (data) {
        const result = currentApi.extractor(data);

        if (result && result.ip) {
          ipElement.text(result.ip);
          ipElement.removeClass("loading").addClass("success");
          statusElement.html("");
          statusElement
            .removeClass("probe-status-loading")
            .addClass("probe-status-success");
        } else {
          // 当前API返回无效数据，尝试下一个
          currentApiIndex++;
          tryNextApi();
        }
      },
      error: function () {
        // 当前API失败，尝试下一个
        currentApiIndex++;
        tryNextApi();
      },
      dataType: "text",
    });
  }

  tryNextApi();
}

// API探测函数
function getProbeIp(url, extractor, elementId, statusId) {
  const ipElement = $("#" + elementId);
  const statusElement = $("#" + statusId);

  $.ajax({
    type: "GET",
    url: url,
    timeout: 10000,
    success: function (data) {
      const result = extractor(data);

      if (result && result.ip) {
        ipElement.text(result.ip);
        ipElement.addClass("success");
        statusElement.html("");
        statusElement
          .removeClass("probe-status-loading")
          .addClass("probe-status-success");
      } else {
        ipElement.text("-");
        ipElement.removeClass("success");
        statusElement.html("<span>暂无数据</span>");
        statusElement
          .removeClass("probe-status-loading")
          .addClass("probe-status-error");
      }
    },
    error: function () {
      ipElement.text("-");
      ipElement.removeClass("success");
      statusElement
        .removeClass("probe-status-loading")
        .addClass("probe-status-error");
    },
    dataType: "text",
  });
}
