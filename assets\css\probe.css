.red0 {
  color: red;
  font-weight: bold;
}

.probe-section {
  margin-top: 30px;
  padding: 25px 0;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 15px;
  backdrop-filter: blur(10px);
}

.probe-card {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 12px;
  padding: 20px 18px;
  margin-bottom: 15px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  min-height: 120px;
  width: 100%;
}

.probe-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.probe-card-header {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}
.probe-card-icon {
  width: 40px;
  height: 40px;
  background: rgba(30, 87, 199, 0.1);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
  color: #1e57c7;
}
.probe-card-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #404b67;
  margin: 0;
  white-space: nowrap;
}

.probe-ip-value {
  font-size: 1.2rem;
  font-weight: 700;
  font-family: "Courier New", monospace;
  color: #1e57c7;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-bottom: 12px;
  min-height: 30px;
  display: flex;
  align-items: center;
  width: 100%;
}

.probe-ip-value.loading {
  color: #0d6efd;
  font-weight: 500;
  font-family: "Karla", sans-serif;
  position: relative;
  display: flex;
  align-items: center;
}

.probe-ip-value.loading::before {
  content: "";
  width: 18px;
  height: 18px;
  border: 2px solid rgba(102, 95, 95, 0.3);
  border-top-color: #0d6efd;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
  flex-shrink: 0;
}

.probe-ip-value.success {
  color: #0d6efd;
}

.probe-status-error {
  background-color: rgba(220, 53, 69, 0.1);
  color: #dc3545;
}

.probe-status-success {
  display: none;
}

.probe-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(248, 150, 30, 0.3);
  border-top-color: #0d6efd;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 6px;
}

/* 增强的加载动画效果 */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 脉冲动画效果 */
@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
}

/* 渐变加载文字效果 */
@keyframes loadingText {
  0% {
    content: "";
  }
  25% {
    content: ".";
  }
  50% {
    content: "..";
  }
  75% {
    content: "...";
  }
  100% {
    content: "";
  }
}

.probe-ip-value.loading::after {
  content: "";
  animation: loadingText 1.5s steps(4, end) infinite;
}

.probe-card-disabled {
  opacity: 0.5;
  pointer-events: none;
}

.probe-card-disabled .probe-card-title {
  color: #999 !important;
}

.probe-ip-value.disabled {
  color: #ccc !important;
  font-weight: normal !important;
}

.location-box {
  margin-top: 20px;
  padding: 15px 20px;
  font-size: 18px;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.badge-campus {
  background-color: #1e57c7;
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 8px;
  margin-left: 8px;
}

.fade-in {
  animation: fadeIn 1s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
